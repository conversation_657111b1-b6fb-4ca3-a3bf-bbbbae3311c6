#!/bin/bash

# Fix Build Dependencies Script
# This script resolves the LVGL dependency issues

set -e

echo "=== Fixing ESP32-S3-TOUCH-LCD-7 Build Dependencies ==="

# Check if we're in the right directory
if [ ! -f "main/main.c" ]; then
    echo "Error: Please run this script from the project root directory"
    exit 1
fi

# Backup original files
echo "Creating backups..."
cp main/idf_component.yml main/idf_component.yml.backup 2>/dev/null || true
cp sdkconfig.defaults.display sdkconfig.defaults.display.backup 2>/dev/null || true

# Fix component dependencies
echo "Fixing component dependencies..."
cat > main/idf_component.yml << 'EOF'
## IDF Component Manager Manifest File
dependencies:
  usb_host_uvc:
    version: "^1.0.4"
    rules:
      - if: "idf_version >= 5.0"
  mdns:
    rules:
      - if: "idf_version >= 5.0"
EOF

# Create a minimal sdkconfig.defaults for display mode
echo "Creating minimal display configuration..."
cat > sdkconfig.defaults.display << 'EOF'
# ESP32-S3-TOUCH-LCD-7 Display Configuration
# Core settings for RGB LCD display

# CPU and Memory Configuration
CONFIG_FREERTOS_HZ=1000
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y

# Flash Configuration
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHFREQ_120M=y

# PSRAM Configuration
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_SPEED_120M=y
CONFIG_SPIRAM_FETCH_INSTRUCTIONS=y
CONFIG_SPIRAM_RODATA=y
CONFIG_ESP32S3_DATA_CACHE_LINE_64B=y

# Compiler Optimization
CONFIG_COMPILER_OPTIMIZATION_PERF=y

# Display Configuration
CONFIG_MODE_LOCAL_DISPLAY=y
CONFIG_ENABLE_TOUCH=y
CONFIG_DISPLAY_BUFFER_SIZE=2

# Frame Configuration
CONFIG_MANUAL_SETTING=y
CONFIG_SIZE_320x240=y
CONFIG_FORMAT_MJPG=y
CONFIG_FRAME_RATE=30

# USB Host Configuration
CONFIG_USB_HOST_CONTROL_TRANSFER_MAX_SIZE=1024
CONFIG_USB_HOST_HW_BUFFER_BIAS_BALANCED=y

# Log Level Configuration
CONFIG_LOG_DEFAULT_LEVEL_INFO=y

# Task Configuration
CONFIG_FREERTOS_UNICORE=n
CONFIG_ESP_TASK_WDT_TIMEOUT_S=10

# Memory Configuration
CONFIG_ESP_MAIN_TASK_STACK_SIZE=8192

# LCD Panel Configuration
CONFIG_LCD_RGB_ISR_IRAM_SAFE=y
CONFIG_LCD_RGB_RESTART_IN_VSYNC=y
EOF

# Clean any existing build artifacts
echo "Cleaning build artifacts..."
rm -rf build/ managed_components/ dependencies.lock 2>/dev/null || true

# Check if ESP-IDF is sourced
if [ -z "$IDF_PATH" ]; then
    echo ""
    echo "⚠️  ESP-IDF environment not detected!"
    echo "Please run: . \$IDF_PATH/export.sh"
    echo "Then run this script again."
    echo ""
    exit 1
fi

# Set target
echo "Setting target to ESP32-S3..."
idf.py set-target esp32s3

# Apply display configuration
echo "Applying display configuration..."
cp sdkconfig.defaults.display sdkconfig.defaults

echo ""
echo "✅ Build dependencies fixed!"
echo ""
echo "Next steps:"
echo "1. Build the project:"
echo "   idf.py build"
echo ""
echo "2. Flash to device:"
echo "   idf.py -p /dev/ttyUSB0 flash monitor"
echo ""
echo "3. To switch to WiFi mode:"
echo "   idf.py menuconfig"
echo "   Navigate to: Application Configuration → Output Mode → WiFi Streaming"
echo ""

# Test build
echo "Testing build configuration..."
if idf.py reconfigure > /dev/null 2>&1; then
    echo "✅ Configuration test passed!"
else
    echo "⚠️  Configuration test failed. Please check ESP-IDF setup."
fi

echo ""
echo "=== Fix Complete ==="
