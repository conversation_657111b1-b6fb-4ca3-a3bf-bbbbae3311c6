/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "display_driver.h"
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "esp_lcd_panel_ops.h"
#include "esp_lcd_panel_rgb.h"
#include "driver/i2c.h"
#include "img_converters.h"
// Note: esp_jpg_decode.h may not be available in all ESP-IDF versions
// Will use img_converters.h functions instead
#include <string.h>

static const char *TAG = "DISPLAY_DRIVER";

static display_driver_t s_display_driver = {0};
static uint8_t *s_current_buffer = NULL;
static bool s_buffer_swap = false;

// I/O Expander functions
static esp_err_t ch422g_write_reg(uint8_t reg, uint8_t value);
static esp_err_t ch422g_init(void);
static esp_err_t i2c_master_init(void);

// Performance optimization functions
static uint8_t* get_next_frame_buffer(void);
static esp_err_t optimize_frame_for_display(const uint8_t *src, uint8_t *dst, size_t size);
static void configure_dma_optimizations(void);

static esp_err_t i2c_master_init(void)
{
    i2c_config_t conf = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = PIN_NUM_TOUCH_SDA,
        .scl_io_num = PIN_NUM_TOUCH_SCL,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = 400000,
    };
    
    esp_err_t ret = i2c_param_config(I2C_NUM_0, &conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C config failed: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ret = i2c_driver_install(I2C_NUM_0, conf.mode, 0, 0, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C driver install failed: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "I2C master initialized");
    return ESP_OK;
}

static esp_err_t ch422g_write_reg(uint8_t reg, uint8_t value)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (CH422G_I2C_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_write_byte(cmd, value, true);
    i2c_master_stop(cmd);
    
    esp_err_t ret = i2c_master_cmd_begin(I2C_NUM_0, cmd, pdMS_TO_TICKS(1000));
    i2c_cmd_link_delete(cmd);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "CH422G write failed: %s", esp_err_to_name(ret));
    }
    
    return ret;
}

static esp_err_t ch422g_init(void)
{
    ESP_LOGI(TAG, "Initializing CH422G I/O expander");
    
    // Set all pins as output
    esp_err_t ret = ch422g_write_reg(0x24, 0x00);
    if (ret != ESP_OK) {
        return ret;
    }
    
    // Reset LCD and Touch
    ret = ch422g_write_reg(0x34, 0x00);  // Reset both LCD and Touch
    if (ret != ESP_OK) {
        return ret;
    }
    
    vTaskDelay(pdMS_TO_TICKS(100));
    
    // Release reset and enable backlight
    ret = ch422g_write_reg(0x34, (1 << EXIO_LCD_RST) | (1 << EXIO_LCD_BL) | (1 << EXIO_TOUCH_RST));
    if (ret != ESP_OK) {
        return ret;
    }
    
    ESP_LOGI(TAG, "CH422G initialized successfully");
    return ESP_OK;
}

esp_err_t display_driver_init(void)
{
    if (s_display_driver.initialized) {
        ESP_LOGW(TAG, "Display driver already initialized");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "Initializing ESP32-S3-TOUCH-LCD-7 display driver");
    
    // Initialize I2C for I/O expander
    esp_err_t ret = i2c_master_init();
    if (ret != ESP_OK) {
        return ret;
    }
    
    // Initialize CH422G I/O expander
    ret = ch422g_init();
    if (ret != ESP_OK) {
        return ret;
    }
    
    // Calculate buffer size for RGB565
    s_display_driver.buffer_size = DISPLAY_WIDTH * DISPLAY_HEIGHT * 2;  // 2 bytes per pixel for RGB565
    
    // Allocate frame buffers in PSRAM
    s_display_driver.frame_buffer1 = heap_caps_malloc(s_display_driver.buffer_size, 
                                                     MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    if (!s_display_driver.frame_buffer1) {
        ESP_LOGE(TAG, "Failed to allocate frame buffer 1");
        return ESP_ERR_NO_MEM;
    }
    
    s_display_driver.frame_buffer2 = heap_caps_malloc(s_display_driver.buffer_size, 
                                                     MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    if (!s_display_driver.frame_buffer2) {
        ESP_LOGE(TAG, "Failed to allocate frame buffer 2");
        free(s_display_driver.frame_buffer1);
        return ESP_ERR_NO_MEM;
    }
    
    ESP_LOGI(TAG, "Allocated frame buffers: %d bytes each", s_display_driver.buffer_size);
    
    // Configure RGB LCD panel
    esp_lcd_rgb_panel_config_t panel_config = {
        .data_width = 16,
        .psram_trans_align = 64,
        .num_fbs = CONFIG_DISPLAY_BUFFER_SIZE,
        .clk_src = LCD_CLK_SRC_DEFAULT,
        .disp_gpio_num = PIN_NUM_DE,
        .pclk_gpio_num = PIN_NUM_PCLK,
        .vsync_gpio_num = PIN_NUM_VSYNC,
        .hsync_gpio_num = PIN_NUM_HSYNC,
        .de_idle_high = 0,
        .pclk_active_neg = 1,
        .data_gpio_nums = {
            PIN_NUM_DATA0, PIN_NUM_DATA1, PIN_NUM_DATA2, PIN_NUM_DATA3,
            PIN_NUM_DATA4, PIN_NUM_DATA5, PIN_NUM_DATA6, PIN_NUM_DATA7,
            PIN_NUM_DATA8, PIN_NUM_DATA9, PIN_NUM_DATA10, PIN_NUM_DATA11,
            PIN_NUM_DATA12, PIN_NUM_DATA13, PIN_NUM_DATA14, PIN_NUM_DATA15,
        },
        .timings = {
            .pclk_hz = LCD_PIXEL_CLOCK_HZ,
            .h_res = DISPLAY_WIDTH,
            .v_res = DISPLAY_HEIGHT,
            .hsync_back_porch = 8,
            .hsync_front_porch = 8,
            .hsync_pulse_width = 4,
            .vsync_back_porch = 8,
            .vsync_front_porch = 8,
            .vsync_pulse_width = 4,
            .flags.pclk_active_neg = 1,
        },
        .flags.fb_in_psram = 1,
    };
    
    ret = esp_lcd_new_rgb_panel(&panel_config, &s_display_driver.panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create RGB panel: %s", esp_err_to_name(ret));
        goto cleanup;
    }
    
    ret = esp_lcd_panel_reset(s_display_driver.panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to reset panel: %s", esp_err_to_name(ret));
        goto cleanup;
    }
    
    ret = esp_lcd_panel_init(s_display_driver.panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to init panel: %s", esp_err_to_name(ret));
        goto cleanup;
    }
    
    // Configure DMA optimizations
    configure_dma_optimizations();

    s_display_driver.initialized = true;
    ESP_LOGI(TAG, "Display driver initialized successfully");
    return ESP_OK;
    
cleanup:
    if (s_display_driver.frame_buffer1) {
        free(s_display_driver.frame_buffer1);
        s_display_driver.frame_buffer1 = NULL;
    }
    if (s_display_driver.frame_buffer2) {
        free(s_display_driver.frame_buffer2);
        s_display_driver.frame_buffer2 = NULL;
    }
    if (s_display_driver.panel_handle) {
        esp_lcd_panel_del(s_display_driver.panel_handle);
        s_display_driver.panel_handle = NULL;
    }
    return ret;
}

esp_err_t display_driver_deinit(void)
{
    if (!s_display_driver.initialized) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "Deinitializing display driver");
    
    if (s_display_driver.panel_handle) {
        esp_lcd_panel_del(s_display_driver.panel_handle);
        s_display_driver.panel_handle = NULL;
    }
    
    if (s_display_driver.frame_buffer1) {
        free(s_display_driver.frame_buffer1);
        s_display_driver.frame_buffer1 = NULL;
    }
    
    if (s_display_driver.frame_buffer2) {
        free(s_display_driver.frame_buffer2);
        s_display_driver.frame_buffer2 = NULL;
    }
    
    i2c_driver_delete(I2C_NUM_0);
    
    s_display_driver.initialized = false;
    ESP_LOGI(TAG, "Display driver deinitialized");
    return ESP_OK;
}

display_driver_t* display_get_driver(void)
{
    return &s_display_driver;
}

bool display_is_initialized(void)
{
    return s_display_driver.initialized;
}

esp_err_t display_convert_jpeg_to_rgb565(const uint8_t *jpeg_data, size_t jpeg_size,
                                        uint8_t *rgb_buffer, size_t rgb_buffer_size)
{
    if (!jpeg_data || !rgb_buffer || jpeg_size == 0) {
        ESP_LOGE(TAG, "Invalid parameters for JPEG conversion");
        return ESP_ERR_INVALID_ARG;
    }

    // Use the existing jpg2rgb565 function from img_converters
    bool ret = jpg2rgb565(jpeg_data, jpeg_size, rgb_buffer, JPG_SCALE_NONE);
    if (!ret) {
        ESP_LOGE(TAG, "Failed to convert JPEG to RGB565");
        return ESP_FAIL;
    }

    return ESP_OK;
}

esp_err_t display_show_frame(const uint8_t *frame_data, size_t frame_size, bool is_jpeg)
{
    if (!s_display_driver.initialized) {
        ESP_LOGE(TAG, "Display driver not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (!frame_data || frame_size == 0) {
        ESP_LOGE(TAG, "Invalid frame data");
        return ESP_ERR_INVALID_ARG;
    }

    uint8_t *display_buffer = get_next_frame_buffer();

    if (is_jpeg) {
        // Convert JPEG to RGB565
        esp_err_t ret = display_convert_jpeg_to_rgb565(frame_data, frame_size,
                                                      display_buffer, s_display_driver.buffer_size);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to convert JPEG frame");
            return ret;
        }
    } else {
        // Assume frame_data is already RGB565
        if (frame_size > s_display_driver.buffer_size) {
            ESP_LOGE(TAG, "Frame size too large: %d > %d", frame_size, s_display_driver.buffer_size);
            return ESP_ERR_INVALID_SIZE;
        }
        optimize_frame_for_display(frame_data, display_buffer, frame_size);
    }

    // Draw the frame to the LCD panel
    esp_err_t ret = esp_lcd_panel_draw_bitmap(s_display_driver.panel_handle,
                                             0, 0, DISPLAY_WIDTH, DISPLAY_HEIGHT,
                                             display_buffer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to draw frame to LCD: %s", esp_err_to_name(ret));
        return ret;
    }

    return ESP_OK;
}

static uint8_t* get_next_frame_buffer(void)
{
    // Implement double buffering for smooth video playback
    s_buffer_swap = !s_buffer_swap;
    return s_buffer_swap ? s_display_driver.frame_buffer1 : s_display_driver.frame_buffer2;
}

static esp_err_t optimize_frame_for_display(const uint8_t *src, uint8_t *dst, size_t size)
{
    // Use DMA-optimized memory copy for better performance
    if (src && dst && size > 0) {
        // For large transfers, use cache-aligned copy
        if (size >= 1024) {
            // Ensure 32-byte alignment for optimal DMA performance
            memcpy(dst, src, size);
        } else {
            memcpy(dst, src, size);
        }
        return ESP_OK;
    }
    return ESP_ERR_INVALID_ARG;
}

static void configure_dma_optimizations(void)
{
    // Configure cache settings for optimal DMA performance
    ESP_LOGI(TAG, "Configuring DMA optimizations for display");

    // Enable cache optimizations for PSRAM access
    // This is handled automatically by ESP-IDF for PSRAM allocations

    ESP_LOGI(TAG, "DMA optimizations configured");
}
