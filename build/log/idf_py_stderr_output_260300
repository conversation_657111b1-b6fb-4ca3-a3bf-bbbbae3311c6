Command: cmake -G Ninja -DPYTHON_DEPS_CHECKED=1 -DPYTHON=/home/<USER>/.espressif/python_env/idf6.0_py3.12_env/bin/python -DESP_PLATFORM=1 -DCCACHE_ENABLE=0 /home/<USER>/Desktop/esp-idf-video-streaming-main
info: INFO: Symbol FRAME_RATE defined in multiple locations (see below). Please check if this is a correct behavior or a random name match:
    /home/<USER>/Desktop/esp-idf-video-streaming-main/main/Kconfig.projbuild:73
    /home/<USER>/Desktop/esp-idf-video-streaming-main/main/Kconfig.projbuild:82
warning: user value 68 on the int symbol LWIP_DHCP_OPTIONS_LEN (defined at /home/<USER>/esp/esp-idf/components/lwip/Kconfig:372) ignored due to being outside the active range ([69, 255]) -- falling back on defaults
CMake Error at /home/<USER>/esp/esp-idf/tools/cmake/tool_version_check.cmake:36 (message):
  

  Too<PERSON> doesn't match supported version from list ['esp-15.1.0_20250607']:
  /home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc


  Please try to run 'idf.py fullclean' to solve it.

Call Stack (most recent call first):
  /home/<USER>/esp/esp-idf/components/esp_common/project_include.cmake:12 (check_expected_tool_version)
  /home/<USER>/esp/esp-idf/tools/cmake/build.cmake:471 (include)
  /home/<USER>/esp/esp-idf/tools/cmake/build.cmake:745 (__build_process_project_includes)
  /home/<USER>/esp/esp-idf/tools/cmake/project.cmake:740 (idf_build_process)
  CMakeLists.txt:6 (project)


