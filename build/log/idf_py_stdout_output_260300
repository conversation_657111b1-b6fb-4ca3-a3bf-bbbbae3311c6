Command: cmake -G Ninja -DPYTHON_DEPS_CHECKED=1 -DPYTHON=/home/<USER>/.espressif/python_env/idf6.0_py3.12_env/bin/python -DESP_PLATFORM=1 -DCCACHE_ENABLE=0 /home/<USER>/Desktop/esp-idf-video-streaming-main
-- Minimal build - OFF
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
NOTICE: Updating lock file at /home/<USER>/Desktop/esp-idf-video-streaming-main/dependencies.lock
NOTICE: Processing 3 dependencies:
NOTICE: [1/3] espressif/mdns (1.8.2)
NOTICE: [2/3] espressif/usb_host_uvc (1.0.4)
NOTICE: [3/3] idf (6.0.0)
-- ESP-TEE is currently supported only on the esp32c6;esp32h2;esp32c5 SoCs
-- Project sdkconfig file /home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig
Loading defaults file /home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults...
/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig:432 CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_NONE was replaced with CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG 
/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig:1301 CONFIG_ESP_SYSTEM_MEMPROT_FEATURE was replaced with CONFIG_ESP_SYSTEM_MEMPROT 
/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig:1302 CONFIG_ESP_SYSTEM_MEMPROT_FEATURE_LOCK was replaced with CONFIG_ESP_SYSTEM_MEMPROT_PMS_LOCK 
-- Compiler supported targets: xtensa-esp-elf
-- Configuring incomplete, errors occurred!
