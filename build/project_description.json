{"version": "1.2", "project_name": "video-streaming", "project_version": "1", "project_path": "/home/<USER>/Desktop/esp-idf-video-streaming-main", "idf_path": "/home/<USER>/esp/v5.5-rc1/esp-idf", "build_dir": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build", "config_file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig", "config_defaults": "/home/<USER>/Desktop/esp-idf-video-streaming-main/sdkconfig.defaults", "bootloader_elf": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/bootloader/bootloader.elf", "app_elf": "video-streaming.elf", "app_bin": "video-streaming.bin", "build_type": "flash_app", "git_revision": "v5.5-rc1-dirty", "target": "esp32s3", "rev": "", "min_rev": "0", "max_rev": "99", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32s3-elf-", "c_compiler": "/home/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc", "config_environment": {"COMPONENT_KCONFIGS": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_trace/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/bt/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_coex/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_common/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ana_cmpr/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_bitscrambler/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_cam/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_dac/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gpio/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gptimer/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2c/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2s/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_isp/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_jpeg/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ledc/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_parlio/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_pcnt/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdm/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_spi/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_touch_sens/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_tsens/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_twai/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_uart/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_usb_serial_jtag/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_eth/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_event/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_gdbstub/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hid/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_client/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_ota/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_server/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_mm/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_partition/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_phy/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_pm/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_psram/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_ringbuf/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_security/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_timer/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/heap/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/ieee802154/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/mbedtls/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/mqtt/esp-mqtt/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_sec_provider/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/openthread/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/pthread/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/tcp_transport/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/ulp/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/vfs/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling/Kconfig;/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/Kconfig;/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__mdns/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader/Kconfig.projbuild;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_app_format/Kconfig.projbuild;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/Kconfig.projbuild;/home/<USER>/esp/v5.5-rc1/esp-idf/components/esptool_py/Kconfig.projbuild;/home/<USER>/esp/v5.5-rc1/esp-idf/components/partition_table/Kconfig.projbuild;/home/<USER>/Desktop/esp-idf-video-streaming-main/main/Kconfig.projbuild"}, "common_component_reqs": ["cxx", "newlib", "freertos", "esp_hw_support", "heap", "log", "soc", "hal", "esp_rom", "esp_common", "esp_system", "xtensa"], "build_components": ["app_trace", "app_update", "bootloader", "bootloader_support", "bt", "cmock", "console", "conversions", "cxx", "driver", "efuse", "esp-tls", "esp_adc", "esp_app_format", "esp_bootloader_format", "esp_coex", "esp_common", "esp_driver_ana_cmpr", "esp_driver_bitscrambler", "esp_driver_cam", "esp_driver_dac", "esp_driver_gpio", "esp_driver_gptimer", "esp_driver_i2c", "esp_driver_i2s", "esp_driver_isp", "esp_driver_jpeg", "esp_driver_ledc", "esp_driver_mcpwm", "esp_driver_parlio", "esp_driver_pcnt", "esp_driver_ppa", "esp_driver_rmt", "esp_driver_sdio", "esp_driver_sdm", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_spi", "esp_driver_touch_sens", "esp_driver_tsens", "esp_driver_twai", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_eth", "esp_event", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_lcd", "esp_local_ctrl", "esp_mm", "esp_netif", "esp_netif_stack", "esp_partition", "esp_phy", "esp_pm", "esp_psram", "esp_ringbuf", "esp_rom", "esp_security", "esp_system", "esp_timer", "esp_vfs_console", "esp_wifi", "espcoredump", "espressif__mdns", "espressif__usb_host_uvc", "esptool_py", "fatfs", "freertos", "hal", "heap", "http_parser", "idf_test", "ieee802154", "json", "log", "lwip", "main", "mbedtls", "mqtt", "newlib", "nvs_flash", "nvs_sec_provider", "openthread", "partition_table", "perfmon", "protobuf-c", "protocomm", "pthread", "rt", "sdmmc", "soc", "spi_flash", "spiffs", "tcp_transport", "touch_element", "ulp", "unity", "usb", "vfs", "wear_levelling", "wifi_provisioning", "wpa_supplicant", "xtensa", ""], "build_component_paths": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_trace", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_update", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bt", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/cmock", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console", "/home/<USER>/Desktop/esp-idf-video-streaming-main/components/conversions", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/cxx", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_app_format", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_bootloader_format", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_coex", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_common", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ana_cmpr", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_bitscrambler", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_cam", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_dac", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gpio", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gptimer", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2s", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_isp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_jpeg", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ledc", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_parlio", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_pcnt", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ppa", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdio", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdm", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdmmc", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdspi", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_spi", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_touch_sens", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_tsens", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_twai", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_uart", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_usb_serial_jtag", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_eth", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_event", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_gdbstub", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hid", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_client", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_ota", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_server", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_local_ctrl", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_mm", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif_stack", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_partition", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_phy", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_pm", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_psram", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_ringbuf", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_security", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_timer", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_vfs_console", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__mdns", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esptool_py", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/heap", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/http_parser", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/idf_test", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/ieee802154", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/json", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip", "/home/<USER>/Desktop/esp-idf-video-streaming-main/main", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/mbedtls", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/mqtt", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_sec_provider", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/openthread", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/partition_table", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/perfmon", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protobuf-c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/pthread", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/rt", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/sdmmc", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/tcp_transport", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/touch_element", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/ulp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/vfs", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/xtensa", ""], "build_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_trace", "type": "LIBRARY", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/app_trace/libapp_trace.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_trace/app_trace.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_trace/app_trace_util.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_trace/host_file_io.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_trace/port/port_uart.c"], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_update", "type": "LIBRARY", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/app_update/libapp_update.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_update/esp_ota_ops.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_update/esp_ota_app_desc.c"], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/bootloader_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/bootloader_common_loader.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/bootloader_clock_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/bootloader_mem.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/bootloader_random.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/bootloader_efuse.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/flash_encrypt.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/secure_boot.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/bootloader_random_esp32s3.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/bootloader_utility.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/flash_partitions.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/esp_image_format.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/bootloader_sha.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support/src/esp32s3/secure_boot_secure_features.c"], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bt", "type": "CONFIG_ONLY", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio", "esp_gdbstub"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/cmock", "type": "LIBRARY", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/cmock/libcmock.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/cmock/CMock/src/cmock.c"], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console", "type": "LIBRARY", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/console/libconsole.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/commands.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/esp_console_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/esp_console_repl_internal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/split_argv.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/linenoise/linenoise.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/esp_console_repl_chip.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_cmd.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_date.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_dbl.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_dstr.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_end.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_file.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_hashtable.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_int.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_lit.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_rem.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_rex.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_str.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/arg_utils.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console/argtable3/argtable3.c"], "include_dirs": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/console"]}, "conversions": {"alias": "idf::conversions", "target": "___idf_conversions", "prefix": "idf", "dir": "/home/<USER>/Desktop/esp-idf-video-streaming-main/components/conversions", "type": "LIBRARY", "lib": "__idf_conversions", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/conversions/libconversions.a", "sources": ["/home/<USER>/Desktop/esp-idf-video-streaming-main/components/conversions/yuv.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/components/conversions/to_jpg.cpp", "/home/<USER>/Desktop/esp-idf-video-streaming-main/components/conversions/jpge.cpp"], "include_dirs": ["include", "private_include"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/cxx", "type": "LIBRARY", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["esp_system", "pthread"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/cxx/libcxx.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/cxx/cxx_exception_stubs.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/cxx/cxx_guards.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/cxx/cxx_init.cpp"], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver", "type": "LIBRARY", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag", "esp_driver_twai"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/driver/libdriver.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/deprecated/adc_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/deprecated/adc_dma_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/deprecated/timer_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/i2c/i2c.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/deprecated/i2s_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/deprecated/mcpwm_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/deprecated/pcnt_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/deprecated/rmt_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/deprecated/sigma_delta_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/deprecated/rtc_temperature_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/touch_sensor/touch_sensor_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/touch_sensor/esp32s3/touch_sensor.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver/twai/twai.c"], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/efuse/libefuse.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse/esp32s3/esp_efuse_table.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse/esp32s3/esp_efuse_fields.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse/esp32s3/esp_efuse_rtc_calib.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse/esp32s3/esp_efuse_utility.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse/src/esp_efuse_api.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse/src/esp_efuse_fields.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse/src/esp_efuse_utility.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse/src/esp_efuse_startup.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls", "type": "LIBRARY", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp-tls/libesp-tls.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls/esp_tls.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls/esp-tls-crypto/esp_tls_crypto.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls/esp_tls_error_capture.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls/esp_tls_platform_port.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls/esp_tls_mbedtls.c"], "include_dirs": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc", "type": "LIBRARY", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_adc/libesp_adc.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/adc_oneshot.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/adc_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/adc_cali.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/adc_cali_curve_fitting.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/deprecated/esp_adc_cal_common_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/adc_continuous.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/adc_monitor.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/gdma/adc_dma.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/adc_filter.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/esp32s3/curve_fitting_coefficients.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc/deprecated/esp32s3/esp_adc_cal_legacy.c"], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_app_format", "type": "LIBRARY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_app_format/libesp_app_format.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_app_format/esp_app_desc.c"], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_bootloader_format", "type": "LIBRARY", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_bootloader_format/libesp_bootloader_format.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_bootloader_format/esp_bootloader_desc.c"], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_coex", "type": "LIBRARY", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_coex/libesp_coex.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_coex/esp32s3/esp_coex_adapter.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_coex/src/coexist_debug_diagram.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_coex/src/coexist_debug.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_common/libesp_common.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ana_cmpr", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_bitscrambler": {"alias": "idf::esp_driver_bitscrambler", "target": "___idf_esp_driver_bitscrambler", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_bitscrambler", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_bitscrambler", "reqs": [], "priv_reqs": ["esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_cam", "type": "LIBRARY", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_cam/libesp_driver_cam.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_cam/esp_cam_ctlr.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_cam/dvp_share_ctrl.c"], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_dac", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gpio", "type": "LIBRARY", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_gpio/libesp_driver_gpio.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gpio/src/gpio.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gpio/src/gpio_glitch_filter_ops.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gpio/src/rtc_io.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gpio/src/dedic_gpio.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gpio/src/gpio_pin_glitch_filter.c"], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gptimer", "type": "LIBRARY", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gptimer/src/gptimer.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gptimer/src/gptimer_common.c"], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2c", "type": "LIBRARY", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_i2c/libesp_driver_i2c.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2c/i2c_master.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2c/i2c_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2c/i2c_slave.c"], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2s", "type": "LIBRARY", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_i2s/libesp_driver_i2s.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2s/i2s_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2s/i2s_std.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2s/i2s_pdm.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2s/i2s_tdm.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2s/i2s_platform.c"], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_isp", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_jpeg", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm", "esp_psram"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ledc", "type": "LIBRARY", "lib": "__idf_esp_driver_ledc", "reqs": ["esp_driver_gpio"], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_ledc/libesp_driver_ledc.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ledc/src/ledc.c"], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm", "type": "LIBRARY", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_cap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_cmpr.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_com.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_fault.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_gen.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_oper.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_sync.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_timer.c"], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_parlio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm", "esp_driver_bitscrambler"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_pcnt", "type": "LIBRARY", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_pcnt/src/pulse_cnt.c"], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ppa", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt", "type": "LIBRARY", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_driver_bitscrambler", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_rmt/libesp_driver_rmt.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt/src/rmt_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt/src/rmt_encoder.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt/src/rmt_encoder_bytes.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt/src/rmt_encoder_copy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt/src/rmt_encoder_simple.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt/src/rmt_rx.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt/src/rmt_tx.c"], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdm", "type": "LIBRARY", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_sdm/libesp_driver_sdm.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdm/src/sdm.c"], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdmmc", "type": "LIBRARY", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdmmc/src/sdmmc_transaction.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdmmc/src/sdmmc_host.c"], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdspi", "type": "LIBRARY", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdspi/src/sdspi_crc.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdspi/src/sdspi_host.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdspi/src/sdspi_transaction.c"], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_spi", "type": "LIBRARY", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_spi/libesp_driver_spi.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_spi/src/gpspi/spi_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_spi/src/gpspi/spi_master.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_spi/src/gpspi/spi_slave.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_spi/src/gpspi/spi_slave_hd.c"], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_touch_sens", "type": "LIBRARY", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_touch_sens/libesp_driver_touch_sens.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_touch_sens/common/touch_sens_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_touch_sens/hw_ver2/touch_version_specific.c"], "include_dirs": ["include", "hw_ver2/include"]}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_tsens", "type": "LIBRARY", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_tsens/libesp_driver_tsens.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_tsens/src/temperature_sensor.c"], "include_dirs": ["include"]}, "esp_driver_twai": {"alias": "idf::esp_driver_twai", "target": "___idf_esp_driver_twai", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_twai", "type": "LIBRARY", "lib": "__idf_esp_driver_twai", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_twai/libesp_driver_twai.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_twai/esp_twai.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_twai/esp_twai_onchip.c"], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_uart", "type": "LIBRARY", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_uart/libesp_driver_uart.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_uart/src/uart.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_uart/src/uart_wakeup.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_uart/src/uhci.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_uart/src/uart_vfs.c"], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_usb_serial_jtag", "type": "LIBRARY", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_connection_monitor.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_vfs.c"], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_eth", "type": "LIBRARY", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_eth/libesp_eth.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_eth/src/esp_eth.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_eth/src/phy/esp_eth_phy_802_3.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_eth/src/esp_eth_netif_glue.c"], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_event", "type": "LIBRARY", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_event/libesp_event.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_event/default_event_loop.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_event/esp_event.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_event/esp_event_private.c"], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_gdbstub", "type": "LIBRARY", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_gdbstub/libesp_gdbstub.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_gdbstub/src/gdbstub.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_gdbstub/src/gdbstub_transport.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_gdbstub/src/packet.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_gdbstub/src/port/xtensa/gdbstub_xtensa.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_gdbstub/src/port/xtensa/gdbstub-entry.S", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_gdbstub/src/port/xtensa/xt_debugexception.S"], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hid", "type": "LIBRARY", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_hid/libesp_hid.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hid/src/esp_hidd.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hid/src/esp_hidh.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hid/src/esp_hid_common.c"], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_client", "type": "LIBRARY", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_http_client/libesp_http_client.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_client/esp_http_client.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_client/lib/http_auth.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_client/lib/http_header.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_client/lib/http_utils.c"], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server", "type": "LIBRARY", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_http_server/libesp_http_server.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server/src/httpd_main.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server/src/httpd_parse.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server/src/httpd_sess.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server/src/httpd_txrx.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server/src/httpd_uri.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server/src/httpd_ws.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server/src/util/ctrl_sock.c"], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_ota", "type": "LIBRARY", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_bootloader_format", "esp_app_format", "esp_event", "esp_partition"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_https_ota/libesp_https_ota.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_ota/src/esp_https_ota.c"], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_server", "type": "LIBRARY", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_https_server/libesp_https_server.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_server/src/https_server.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/cpu.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/esp_cpu_intr.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/esp_memory_utils.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/cpu_region_protect.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/esp_clk.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/clk_ctrl_os.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/hw_random.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/intr_alloc.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/mac_addr.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/periph_ctrl.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/revision.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/rtc_module.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/regi2c_ctrl.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/esp_gpio_reserve.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/sar_periph_ctrl_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/io_mux.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/esp_clk_tree.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/dma/esp_dma_utils.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/dma/gdma_link.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/spi_bus_lock.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/clk_utils.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp_clk_tree_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/spi_share_hw_ctrl.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/adc_share_hw_ctrl.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/sleep_modem.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/sleep_modes.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/sleep_console.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/sleep_usb.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/sleep_gpio.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/sleep_event.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/dma/gdma.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/deprecated/gdma_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/dma/esp_async_memcpy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/dma/async_memcpy_gdma.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/systimer.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/mspi_timing_tuning/mspi_timing_tuning.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/sleep_wake_stub.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/esp_clock_output.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/power_supply/brownout.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/rtc_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/rtc_sleep.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/rtc_time.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/chip_info.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/sar_periph_ctrl.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp32s3/esp_memprot.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/port/esp_memprot_conv.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/mspi_timing_tuning/port/esp32s3/mspi_timing_config.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/mspi_timing_tuning/port/esp32s3/mspi_timing_by_mspi_delay.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support/lowpower/port/esp32s3/sleep_cpu.c"], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include", "debug_probe/include", "mspi_timing_tuning/include", "mspi_timing_tuning/tuning_scheme_impl/include", "power_supply/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd", "type": "LIBRARY", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_lcd/libesp_lcd.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/src/esp_lcd_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/src/esp_lcd_panel_io.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/src/esp_lcd_panel_nt35510.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/src/esp_lcd_panel_ssd1306.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/src/esp_lcd_panel_st7789.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/src/esp_lcd_panel_ops.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v1.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v2.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/spi/esp_lcd_panel_io_spi.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/i80/esp_lcd_panel_io_i80.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd/rgb/esp_lcd_panel_rgb.c"], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_local_ctrl", "type": "LIBRARY", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c", "esp_netif"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_handler.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_local_ctrl/proto-c/esp_local_ctrl.pb-c.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_transport_httpd.c"], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_mm", "type": "LIBRARY", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_mm/libesp_mm.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_mm/esp_mmu_map.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_mm/port/esp32s3/ext_mem_layout.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_mm/esp_cache_msync.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_mm/esp_cache_utils.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_mm/heap_align_hw.c"], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif", "type": "LIBRARY", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_netif/libesp_netif.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif/esp_netif_handlers.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif/esp_netif_objects.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif/esp_netif_defaults.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif/lwip/esp_netif_lwip.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif/lwip/esp_netif_sntp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif/lwip/esp_netif_lwip_defaults.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif/lwip/netif/wlanif.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif/lwip/netif/ethernetif.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif/lwip/netif/esp_pbuf_ref.c"], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif_stack", "type": "CONFIG_ONLY", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_partition", "type": "LIBRARY", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_partition/libesp_partition.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_partition/partition.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_partition/partition_target.c"], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_phy", "type": "LIBRARY", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "esp_driver_gpio", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_phy/libesp_phy.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_phy/src/phy_override.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_phy/src/lib_printf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_phy/src/phy_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_phy/src/phy_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_phy/esp32s3/phy_init_data.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_phy/src/btbb_init.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_pm", "type": "LIBRARY", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_pm/libesp_pm.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_pm/pm_locks.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_pm/pm_trace.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_pm/pm_impl.c"], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_psram", "type": "LIBRARY", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_psram/libesp_psram.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_psram/system_layer/esp_psram.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_psram/esp32s3/esp_psram_impl_octal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_psram/device", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_psram/xip_impl/mmu_psram_flash.c"], "include_dirs": ["include", "xip_impl/include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_ringbuf", "type": "LIBRARY", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_ringbuf/libesp_ringbuf.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_ringbuf/ringbuf.c"], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_rom/libesp_rom.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_sys.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_print.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_crc.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_uart.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_spiflash.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_efuse.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_gpio.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_longjmp.S", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_systimer.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_wdt.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_cache_esp32s2_esp32s3.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S"], "include_dirs": ["include", "esp32s3/include", "esp32s3/include/esp32s3", "esp32s3"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_security", "type": "LIBRARY", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_security/libesp_security.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_security/src/init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_security/src/esp_hmac.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_security/src/esp_ds.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_security/src/esp_crypto_lock.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_security/src/esp_crypto_periph_clk.c"], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_system/libesp_system.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/esp_err.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/crosscore_int.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/esp_ipc.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/esp_system_console.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/freertos_hooks.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/int_wdt.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/panic.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/esp_system.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/startup.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/startup_funcs.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/system_time.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/stack_check.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/ubsan.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/xt_wdt.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/task_wdt/task_wdt.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/task_wdt/task_wdt_impl_timergroup.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/cpu_start.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/panic_handler.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/esp_system_chip.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/image_process.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/esp_ipc_isr.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_port.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_handler.S", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_routines.S", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/arch/xtensa/panic_arch.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/arch/xtensa/panic_handler_asm.S", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/arch/xtensa/expression_with_stack.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/arch/xtensa/expression_with_stack_asm.S", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/arch/xtensa/debug_helpers.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/arch/xtensa/debug_helpers_asm.S", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/arch/xtensa/debug_stubs.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/arch/xtensa/trax.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/soc/esp32s3/highint_hdl.S", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/soc/esp32s3/clk.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/soc/esp32s3/reset_reason.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/soc/esp32s3/system_internal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/soc/esp32s3/cache_err_int.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system/port/soc/esp32s3/apb_backup_dma.c"], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_timer", "type": "LIBRARY", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_timer/libesp_timer.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_timer/src/esp_timer.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_timer/src/esp_timer_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_timer/src/ets_timer_legacy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_timer/src/system_time.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_timer/src/esp_timer_impl_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_timer/src/esp_timer_impl_systimer.c"], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_vfs_console", "type": "LIBRARY", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_vfs_console/libesp_vfs_console.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_vfs_console/vfs_console.c"], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi", "type": "LIBRARY", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/esp_wifi/libesp_wifi.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi/src/lib_printf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi/src/mesh_event.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi/src/smartconfig.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi/src/wifi_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi/src/wifi_default.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi/src/wifi_netif.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi/src/wifi_default_ap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi/esp32s3/esp_adapter.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi/regulatory/esp_wifi_regulatory.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi/src/smartconfig_ack.c"], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump", "type": "LIBRARY", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/espcoredump/libespcoredump.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump/src/core_dump_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump/src/core_dump_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump/src/core_dump_flash.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump/src/core_dump_uart.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump/src/core_dump_elf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump/src/core_dump_binary.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump/src/core_dump_sha.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump/src/core_dump_crc.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump/src/port/xtensa/core_dump_port.c"], "include_dirs": ["include", "include/port/xtensa"]}, "espressif__mdns": {"alias": "idf::espressif__mdns", "target": "___idf_espressif__mdns", "prefix": "idf", "dir": "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__mdns", "type": "LIBRARY", "lib": "__idf_espressif__mdns", "reqs": ["lwip", "console", "esp_netif"], "priv_reqs": ["esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/espressif__mdns/libespressif__mdns.a", "sources": ["/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__mdns/mdns.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__mdns/mdns_mem_caps.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__mdns/mdns_networking_lwip.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__mdns/mdns_console.c"], "include_dirs": ["include"]}, "espressif__usb_host_uvc": {"alias": "idf::espressif__usb_host_uvc", "target": "___idf_espressif__usb_host_uvc", "prefix": "idf", "dir": "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc", "type": "LIBRARY", "lib": "__idf_espressif__usb_host_uvc", "reqs": ["usb", "pthread"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/espressif__usb_host_uvc/libespressif__usb_host_uvc.a", "sources": ["/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc/libuvc/src/ctrl.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc/libuvc/src/ctrl-gen.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc/libuvc/src/device.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc/libuvc/src/diag.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc/libuvc/src/frame.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc/libuvc/src/init.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc/libuvc/src/misc.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc/libuvc/src/stream.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc/src/descriptor.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc/src/libusb_adapter.c"], "include_dirs": ["include", "libuvc/include"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs", "type": "LIBRARY", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/fatfs/libfatfs.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs/diskio/diskio.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs/diskio/diskio_rawflash.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs/diskio/diskio_wl.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs/src/ff.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs/src/ffunicode.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs/port/freertos/ffsystem.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs/diskio/diskio_sdmmc.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs/vfs/vfs_fat.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs/vfs/vfs_fat_sdmmc.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs/vfs/vfs_fat_spiflash.c"], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos", "type": "LIBRARY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/freertos/libfreertos.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/heap_idf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/app_startup.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/port_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/port_systick.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/FreeRTOS-Kernel/list.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/FreeRTOS-Kernel/queue.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/FreeRTOS-Kernel/tasks.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/FreeRTOS-Kernel/timers.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/FreeRTOS-Kernel/event_groups.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/FreeRTOS-Kernel/stream_buffer.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/portasm.S", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/esp_additions/freertos_compatibility.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/esp_additions/idf_additions_event_groups.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos/esp_additions/idf_additions.c"], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/hal/libhal.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/hal_utils.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/mpu_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/efuse_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/esp32s3/efuse_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/mmu_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/cache_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/color_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/spi_flash_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/spi_flash_hal_iram.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/spi_flash_encrypt_hal_iram.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/esp32s3/clk_tree_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/systimer_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/uart_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/uart_hal_iram.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/gpio_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/rtc_io_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/timer_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/ledc_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/ledc_hal_iram.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/i2c_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/i2c_hal_iram.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/rmt_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/pcnt_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/mcpwm_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/uhci_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/twai_hal_sja1000.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/gdma_hal_top.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/gdma_hal_ahb_v1.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/i2s_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/sdm_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/sdmmc_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/adc_hal_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/adc_oneshot_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/adc_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/lcd_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/mpi_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/sha_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/aes_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/brownout_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/spi_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/spi_hal_iram.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/spi_slave_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/spi_slave_hal_iram.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/spi_slave_hd_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/spi_flash_hal_gpspi.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/hmac_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/ds_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/usb_serial_jtag_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/usb_dwc_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/usb_wrap_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/esp32s3/touch_sensor_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/touch_sensor_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/touch_sens_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/xt_wdt_hal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal/esp32s3/rtc_cntl_hal.c"], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/heap", "type": "LIBRARY", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/heap/libheap.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/heap/heap_caps_base.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/heap/heap_caps.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/heap/heap_caps_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/heap/multi_heap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/heap/tlsf/tlsf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/heap/port/memory_layout_utils.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/heap/port/esp32s3/memory_layout.c"], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/http_parser", "type": "LIBRARY", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/http_parser/libhttp_parser.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/http_parser/http_parser.c"], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/idf_test", "type": "CONFIG_ONLY", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/ieee802154", "type": "CONFIG_ONLY", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "esp_timer", "soc", "hal", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/json", "type": "LIBRARY", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/json/libjson.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/json/cJSON/cJSON.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/json/cJSON/cJSON_Utils.c"], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": ["hal", "soc", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/log/liblog.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/os/log_timestamp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/log_timestamp_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/os/log_lock.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/buffer/log_buffers.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/os/util.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/util.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/log_format_text.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/log_print.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/log.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/os/log_write.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/log_level/log_level.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/log_level/tag_log_level/tag_log_level.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/log_level/tag_log_level/linked_list/log_linked_list.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log/src/log_level/tag_log_level/cache/log_binary_heap.c"], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip", "type": "LIBRARY", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/lwip/liblwip.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/apps/sntp/sntp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/api/api_lib.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/api/api_msg.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/api/err.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/api/if_api.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/api/netbuf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/api/netdb.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/api/netifapi.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/api/sockets.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/api/tcpip.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/apps/sntp/sntp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/apps/netbiosns/netbiosns.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/def.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/dns.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/inet_chksum.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ip.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/mem.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/memp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/netif.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/pbuf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/raw.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/stats.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/sys.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/tcp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/tcp_in.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/tcp_out.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/timeouts.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/udp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv4/autoip.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv4/dhcp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv4/etharp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv4/icmp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv4/igmp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_napt.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_addr.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_frag.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv6/dhcp6.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv6/ethip6.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv6/icmp6.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv6/inet6.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv6/ip6.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_addr.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_frag.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv6/mld6.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/core/ipv6/nd6.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ethernet.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/bridgeif.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/bridgeif_fdb.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/slipif.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/auth.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/ccp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/chap-md5.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/chap-new.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/chap_ms.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/demand.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/eap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/ecp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/eui64.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/fsm.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/ipcp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/ipv6cp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/lcp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/magic.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/mppe.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/multilink.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/ppp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/pppapi.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/pppcrypt.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/pppoe.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/pppol2tp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/pppos.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/upap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/utils.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/vj.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/port/hooks/tcp_isn_default.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/port/hooks/lwip_default_hooks.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/port/debug/lwip_debug.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/port/sockets_ext.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/port/freertos/sys_arch.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/port/if_index.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/port/acd_dhcp_check.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/port/esp32xx/vfs_lwip.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/apps/ping/esp_ping.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/apps/ping/ping.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/apps/ping/ping_sock.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/arc4.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/des.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md4.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md5.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/sha1.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip/apps/dhcpserver/dhcpserver.c"], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "/home/<USER>/Desktop/esp-idf-video-streaming-main/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": [], "priv_reqs": ["espressif__mdns", "espressif__usb_host_uvc"], "managed_reqs": [], "managed_priv_reqs": ["espressif__mdns", "espressif__usb_host_uvc"], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/main/libmain.a", "sources": ["/home/<USER>/Desktop/esp-idf-video-streaming-main/main/main.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/main/http_server.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/main/display_driver.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/main/app_config.c"], "include_dirs": ["."]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/mbedtls", "type": "LIBRARY", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/mbedtls/libmbedtls.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/mbedtls/esp_crt_bundle/esp_crt_bundle.c", "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/x509_crt_bundle.S"], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/mqtt", "type": "LIBRARY", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/mqtt/libmqtt.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/mqtt/esp-mqtt/mqtt_client.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_msg.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_outbox.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/mqtt/esp-mqtt/lib/platform_esp32_idf.c"], "include_dirs": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib", "type": "LIBRARY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/newlib/libnewlib.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/abort.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/assert.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/heap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/locks.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/poll.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/pthread.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/random.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/getentropy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/termios.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/stdatomic.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/time.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/sysconf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/realpath.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/scandir.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/syscalls.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/reent_syscalls.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/port/esp_time_impl.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/port/xtensa/stdatomic_s32c1i.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/flockfile.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/reent_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/newlib_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib/src/port/esp_time_impl.c"], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash", "type": "LIBRARY", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/nvs_flash/libnvs_flash.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_api.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_cxx_api.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_item_hash_list.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_page.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_pagemanager.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_storage.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_handle_simple.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_handle_locked.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_partition.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_partition_lookup.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_partition_manager.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_types.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_platform.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_bootloader.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_encrypted_partition.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_bootloader_aes.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash/src/nvs_bootloader_xts_aes.c"], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_sec_provider", "type": "LIBRARY", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/nvs_sec_provider/libnvs_sec_provider.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_sec_provider/nvs_sec_provider.c"], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/openthread", "type": "CONFIG_ONLY", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/perfmon", "type": "LIBRARY", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/perfmon/libperfmon.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/perfmon/xtensa_perfmon_access.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/perfmon/xtensa_perfmon_apis.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/perfmon/xtensa_perfmon_masks.c"], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protobuf-c", "type": "LIBRARY", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/protobuf-c/libprotobuf-c.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/protobuf-c/protobuf-c/protobuf-c/protobuf-c.c"], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm", "type": "LIBRARY", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/protocomm/libprotocomm.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/src/common/protocomm.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/proto-c/constants.pb-c.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/proto-c/sec0.pb-c.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/proto-c/sec1.pb-c.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/proto-c/sec2.pb-c.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/proto-c/session.pb-c.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/src/transports/protocomm_console.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/src/transports/protocomm_httpd.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/src/security/security0.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/src/security/security1.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/src/security/security2.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp_mpi.c"], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/pthread", "type": "LIBRARY", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/pthread/libpthread.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/pthread/pthread.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/pthread/pthread_cond_var.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/pthread/pthread_local_storage.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/pthread/pthread_rwlock.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/pthread/pthread_semaphore.c"], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/rt", "type": "LIBRARY", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/rt/librt.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/rt/FreeRTOS_POSIX_mqueue.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/rt/FreeRTOS_POSIX_utils.c"], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/sdmmc", "type": "LIBRARY", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/sdmmc/libsdmmc.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/sdmmc/sdmmc_cmd.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/sdmmc/sdmmc_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/sdmmc/sdmmc_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/sdmmc/sdmmc_io.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/sdmmc/sdmmc_mmc.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/sdmmc/sdmmc_sd.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/sdmmc/sd_pwr_ctrl/sd_pwr_ctrl.c"], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/soc/libsoc.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/lldesc.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/dport_access_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/interrupts.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/gpio_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/uart_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/adc_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/dedic_gpio_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/gdma_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/spi_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/ledc_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/pcnt_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/rmt_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/sdm_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/i2s_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/i2c_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/temperature_sensor_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/timer_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/lcd_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/mcpwm_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/mpi_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/sdmmc_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/touch_sensor_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/twai_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/wdt_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/usb_dwc_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/rtc_io_periph.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc/esp32s3/power_supply_periph.c"], "include_dirs": ["include", "esp32s3", "esp32s3/include", "esp32s3/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/spi_flash/libspi_flash.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/flash_brownout_hook.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/esp32s3/spi_flash_oct_flash_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_hpm_enable.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_chip_drivers.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_chip_generic.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_chip_issi.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_chip_mxic.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_chip_gd.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_chip_winbond.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_chip_boya.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_chip_mxic_opi.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_chip_th.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/memspi_host_driver.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/cache_utils.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/flash_mmap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/flash_ops.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_wrap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/esp_flash_api.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/esp_flash_spi_init.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_os_func_app.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash/spi_flash_os_func_noos.c"], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs", "type": "LIBRARY", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/spiffs/libspiffs.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs/spiffs_api.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs/spiffs/src/spiffs_cache.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs/spiffs/src/spiffs_check.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs/spiffs/src/spiffs_gc.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs/spiffs/src/spiffs_hydrogen.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs/spiffs/src/spiffs_nucleus.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs/esp_spiffs.c"], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/tcp_transport", "type": "LIBRARY", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/tcp_transport/libtcp_transport.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/tcp_transport/transport.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/tcp_transport/transport_ssl.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/tcp_transport/transport_internal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/tcp_transport/transport_socks_proxy.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/tcp_transport/transport_ws.c"], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/touch_element", "type": "LIBRARY", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/touch_element/libtouch_element.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/touch_element/touch_element.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/touch_element/touch_button.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/touch_element/touch_slider.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/touch_element/touch_matrix.c"], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/ulp", "type": "CONFIG_ONLY", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity", "type": "LIBRARY", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/unity/libunity.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity/unity/src/unity.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity/unity_compat.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity/unity_runner.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity/unity_utils_freertos.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity/unity_utils_cache.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity/unity_utils_memory.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity/unity_port_esp32.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity/port/esp/unity_utils_memory_esp.c"], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb", "type": "LIBRARY", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/usb/libusb.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb/hcd_dwc.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb/enum.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb/hub.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb/usb_helpers.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb/usb_host.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb/usb_private.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb/usbh.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb/usb_phy.c"], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/vfs", "type": "LIBRARY", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/vfs/libvfs.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/vfs/vfs.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/vfs/vfs_eventfd.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/vfs/vfs_semihost.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/vfs/nullfs.c"], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling", "type": "LIBRARY", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/wear_levelling/libwear_levelling.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling/Partition.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling/SPI_Flash.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling/WL_Ext_Perf.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling/WL_Ext_Safe.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling/WL_Flash.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling/crc32.cpp", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling/wear_levelling.cpp"], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning", "type": "LIBRARY", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/wifi_provisioning/libwifi_provisioning.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/src/wifi_config.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/src/wifi_scan.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/src/wifi_ctrl.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/src/manager.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/src/handlers.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/src/scheme_console.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/proto-c/wifi_config.pb-c.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/proto-c/wifi_scan.pb-c.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/proto-c/wifi_ctrl.pb-c.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/proto-c/wifi_constants.pb-c.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning/src/scheme_softap.c"], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant", "type": "LIBRARY", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/wpa_supplicant/libwpa_supplicant.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/port/os_xtensa.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/port/eloop.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/ap/ap_config.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/ap/ieee802_1x.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/ap/wpa_auth.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/ap/wpa_auth_ie.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/ap/pmksa_cache_auth.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/ap/sta_info.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/ap/ieee802_11.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/ap/comeback_token.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/common/sae.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/common/dragonfly.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/common/wpa_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/utils/bitfield.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/aes-siv.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/sha256-kdf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/ccmp.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/aes-gcm.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/crypto_ops.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/dh_group5.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/dh_groups.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/ms_funcs.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/sha1-tlsprf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/sha256-tlsprf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/sha384-tlsprf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/sha256-prf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/sha1-prf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/sha384-prf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/md4-internal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/sha1-tprf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_common/eap_wsc_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/common/ieee802_11_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/chap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/eap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_mschapv2.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_ttls.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/mschapv2.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_pac.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/rsn_supp/pmksa_cache.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa_ie.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/utils/base64.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/utils/common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/utils/ext_password.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/utils/uuid.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/utils/wpabuf.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/utils/wpa_debug.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/utils/json.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/wps/wps.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/wps/wps_attr_build.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/wps/wps_attr_parse.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/wps/wps_attr_process.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/wps/wps_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/wps/wps_dev_attr.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/wps/wps_enrollee.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/common/sae_pk.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_eap_client.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa2_api_port.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa_main.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpas_glue.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_common.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wps.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa3.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_owe.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_hostap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/fastpsk.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/rc4.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/des-internal.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/fastpbkdf2.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/aes-wrap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/aes-unwrap.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant/src/crypto/aes-ccm.c"], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/xtensa", "type": "LIBRARY", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/esp-idf/xtensa/libxtensa.a", "sources": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/xtensa/eri.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/xtensa/xt_trax.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/xtensa/xtensa_context.S", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/xtensa/xtensa_intr_asm.S", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/xtensa/xtensa_intr.c", "/home/<USER>/esp/v5.5-rc1/esp-idf/components/xtensa/xtensa_vectors.S"], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}}, "all_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_trace", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/app_update", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bootloader_support", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/bt", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio", "esp_gdbstub"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/cmock", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/console", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/cxx", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["esp_system", "pthread"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/driver", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag", "esp_driver_twai"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/efuse", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_adc", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_app_format", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_bootloader_format", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_coex", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_common", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ana_cmpr", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_bitscrambler": {"alias": "idf::esp_driver_bitscrambler", "target": "___idf_esp_driver_bitscrambler", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_bitscrambler", "lib": "__idf_esp_driver_bitscrambler", "reqs": [], "priv_reqs": ["esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_cam", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_dac", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gpio", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_gptimer", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2c", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_i2s", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_isp", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_jpeg", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm", "esp_psram"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ledc", "lib": "__idf_esp_driver_ledc", "reqs": ["esp_driver_gpio"], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_mcpwm", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_parlio", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm", "esp_driver_bitscrambler"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_pcnt", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_ppa", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_rmt", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_driver_bitscrambler", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdio", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdm", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdmmc", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_sdspi", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_spi", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_touch_sens", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "hw_ver2/include"]}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_tsens", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_twai": {"alias": "idf::esp_driver_twai", "target": "___idf_esp_driver_twai", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_twai", "lib": "__idf_esp_driver_twai", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_uart", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_driver_usb_serial_jtag", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_eth", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_event", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_gdbstub", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hid", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_client", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_http_server", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_ota", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_bootloader_format", "esp_app_format", "esp_event", "esp_partition"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_https_server", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_hw_support", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include", "debug_probe/include", "mspi_timing_tuning/include", "mspi_timing_tuning/tuning_scheme_impl/include", "power_supply/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_lcd", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_local_ctrl", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c", "esp_netif"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_mm", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_netif_stack", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_partition", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_phy", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "esp_driver_gpio", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_pm", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_psram", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "xip_impl/include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_ringbuf", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_rom", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include", "esp32s3/include/esp32s3", "esp32s3"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_security", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_system", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_tee": {"alias": "idf::esp_tee", "target": "___idf_esp_tee", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_tee", "lib": "__idf_esp_tee", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_timer", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_vfs_console", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esp_wifi", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/espcoredump", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/esptool_py", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/fatfs", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/freertos", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/hal", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/heap", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/http_parser", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/idf_test", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/ieee802154", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "esp_timer", "soc", "hal", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/json", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "linux": {"alias": "idf::linux", "target": "___idf_linux", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/linux", "lib": "__idf_linux", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/log", "lib": "__idf_log", "reqs": [], "priv_reqs": ["hal", "soc", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/lwip", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/mbedtls", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/mqtt", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["/home/<USER>/esp/v5.5-rc1/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/newlib", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_flash", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/nvs_sec_provider", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/openthread", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/partition_table", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/perfmon", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protobuf-c", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/protocomm", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/pthread", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/riscv", "lib": "__idf_riscv", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/rt", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/sdmmc", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/soc", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3", "esp32s3/include", "esp32s3/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spi_flash", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/spiffs", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/tcp_transport", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/touch_element", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/ulp", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/unity", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/usb", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/vfs", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wear_levelling", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wifi_provisioning", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/wpa_supplicant", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "/home/<USER>/esp/v5.5-rc1/esp-idf/components/xtensa", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "/home/<USER>/Desktop/esp-idf-video-streaming-main/main", "lib": "__idf_main", "reqs": [], "priv_reqs": ["espressif__mdns", "espressif__usb_host_uvc"], "managed_reqs": [], "managed_priv_reqs": ["espressif__mdns", "espressif__usb_host_uvc"], "include_dirs": ["."]}, "conversions": {"alias": "idf::conversions", "target": "___idf_conversions", "prefix": "idf", "dir": "/home/<USER>/Desktop/esp-idf-video-streaming-main/components/conversions", "lib": "__idf_conversions", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "private_include"]}, "espressif__mdns": {"alias": "idf::espressif__mdns", "target": "___idf_espressif__mdns", "prefix": "idf", "dir": "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__mdns", "lib": "__idf_espressif__mdns", "reqs": ["lwip", "console", "esp_netif"], "priv_reqs": ["esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "espressif__usb_host_uvc": {"alias": "idf::espressif__usb_host_uvc", "target": "___idf_espressif__usb_host_uvc", "prefix": "idf", "dir": "/home/<USER>/Desktop/esp-idf-video-streaming-main/managed_components/espressif__usb_host_uvc", "lib": "__idf_espressif__usb_host_uvc", "reqs": ["usb", "pthread"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "libuvc/include"]}}, "debug_prefix_map_gdbinit": "", "gdbinit_files": {"01_symbols": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/gdbinit/symbols", "02_prefix_map": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/gdbinit/prefix_map", "03_py_extensions": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/gdbinit/py_extensions", "04_connect": "/home/<USER>/Desktop/esp-idf-video-streaming-main/build/gdbinit/connect"}, "debug_arguments_openocd": "-f board/esp32s3-builtin.cfg"}