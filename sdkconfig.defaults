# ESP32-S3 Video Streaming Configuration
# Fixed partition table configuration

# Target
CONFIG_IDF_TARGET="esp32s3"

# Partition Table - Single factory app (default)
CONFIG_PARTITION_TABLE_SINGLE_APP=y

# Flash Configuration
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y

# PSRAM Configuration
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_SPEED_80M=y

# CPU Configuration
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y
CONFIG_FREERTOS_HZ=1000

# Compiler Optimization
CONFIG_COMPILER_OPTIMIZATION_SIZE=y

# Application Configuration
CONFIG_MODE_WIFI_STREAMING=y

# Frame Configuration
CONFIG_SIZE_320x240=y
CONFIG_FORMAT_MJPG=y
CONFIG_FRAME_RATE=15

# Log Configuration
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_BOOTLOADER_LOG_LEVEL_WARN=y

# Memory Configuration
CONFIG_ESP_MAIN_TASK_STACK_SIZE=4096
